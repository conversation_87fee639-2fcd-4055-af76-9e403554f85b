#!/usr/bin/env python3

import os
import shutil
import numpy as np


def main():
    mValue = 0.0
    lpValue = 0.0
    omegaValue1 = 0.0
    omegaValue2 = 1.0
    omegaValuestep = 0.02

    source_file = 'OM_inertial_v1.py'
    omega_values = np.arange(omegaValue1, omegaValue2 + omegaValuestep, omegaValuestep)
    created_folders = []
    for omega_val in omega_values:
        folder_name = f"m_{mValue}_omega_{omega_val:.3f}_lp_{lpValue}"
        try:
            if not os.path.exists(folder_name):
                os.makedirs(folder_name)
                print(f"Created folder: {folder_name}")
            else:
                print(f"Folder already exists: {folder_name}")

            # Copy OM_inertial_v1.py to the folder
            dest_file = os.path.join(folder_name, 'OM_inertial_v1.py')
            shutil.copy2(source_file, dest_file)
            # Create para.txt with parameter values
            para_file = os.path.join(folder_name, 'para.txt')
            with open(para_file, 'w') as f:
                f.write(f"{mValue}\n")
                f.write(f"{omega_val:.4f}\n")
                f.write(f"{lpValue}\n")

        except Exception as e:
            print(f"Error creating folder {folder_name}: {e}")



if __name__ == "__main__":
    main()
