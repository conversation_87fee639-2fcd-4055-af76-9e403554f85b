#!/usr/bin/env python3

import os
import glob
import numpy as np

def collect_OM_data():

    data_list = []
    pattern = "**/OM_value.txt"
    om_files = glob.glob(pattern, recursive=True)
    
    print(f"Found {len(om_files)} OM_value.txt files")
    
    for file_path in om_files:
        try:
            # Read the file
            with open(file_path, 'r') as f:
                lines = f.readlines()

            if len(lines) < 2:
                print(f"Warning: {file_path} has less than 2 lines, skipping...")
                continue
            m_value = float(lines[0].strip())
            OM_value = float(lines[1].strip())
            
            data_list.append((m_value, OM_value, file_path))
            print(f"Read {file_path}: m={m_value:.6e}, OM={OM_value:.6e}")
            
        except Exception as e:
            print(f"Error reading {file_path}: {e}")
            continue
    
    return data_list

def save_results(data_list, output_file='results.txt'):

    if not data_list:
        print("No data to save!")
        return
    
    # Sort by m value (first element of tuple)
    sorted_data = sorted(data_list, key=lambda x: x[0])
    
    print(f"\nSaving {len(sorted_data)} entries to {output_file}")
    
    with open(output_file, 'w') as f:
        for m_value, OM_value, file_path in sorted_data:
            f.write(f"{m_value:.6e} {OM_value:.6e}\n")
    
    print(f"Results saved to {output_file}")


def plot_results(data_list, save_plot=True):
    """
    Plot m vs OM values
    """
    try:
        import matplotlib.pyplot as plt
        
        if not data_list:
            print("No data to plot!")
            return
        
        # Sort by m value
        sorted_data = sorted(data_list, key=lambda x: x[0])
        
        m_values = [data[0] for data in sorted_data]
        OM_values = [data[1] for data in sorted_data]
        
        plt.figure(figsize=(10, 6))
        plt.plot(m_values, OM_values, 'bo-', linewidth=2, markersize=6)
        plt.xlabel('m ', fontsize=12)
        plt.ylabel('OM ', fontsize=12)

        # Use scientific notation for axes if needed
        #plt.ticklabel_format(style='scientific', axis='both', scilimits=(-3,3))
        
        plt.tight_layout()
        
        if save_plot:
            plt.savefig('OM_vs_m_plot.png', dpi=300, bbox_inches='tight')
            print("Plot saved to OM_vs_m_plot.png")
        
        plt.show()
        
    except ImportError:
        print("Matplotlib not available, skipping plot generation")

def main():

    # Get current directory
    current_dir = os.getcwd()
    print(f"Working directory: {current_dir}")
    
    # Collect data from all OM_value.txt files
    data_list = collect_OM_data()
    
    if not data_list:
        print("No OM_value.txt files found or no valid data!")
        return
    
    # Save main results file
    save_results(data_list, 'results.txt')
    plot_results(data_list, save_plot=True)

if __name__ == "__main__":
    main()
