#!/usr/bin/env python3
"""
Shooting method solver for the coupled ODE system with fourth-order derivatives
Solves the system:
phi1''(t) = -phi3'(t)*sin(phi3(t)) + m*(phi3''(t)*sin(phi3(t)) + (phi3'(t))^2*cos(phi3(t)) + m*phi1''''(t))
phi2''(t) = phi3'(t)*cos(phi3(t)) - m*(phi3''(t)*cos(phi3(t)) - (phi3'(t))^2*sin(phi3(t)) - m*phi2''''(t))
phi3''(t) = phi1'(t)*sin(phi3(t)) - phi2'(t)*cos(phi3(t)) + m*(phi1''(t)*sin(phi3(t)) - phi2''(t)*cos(phi3(t)) + I_p^2*phi3''''(t)/m)

Boundary conditions:
phi1(t0) = phi1_L, phi1(tf) = phi1_R
phi2(t0) = phi2_L, phi2(tf) = phi2_R
phi3(t0) = phi3_L, phi3(tf) = phi3_R
"""

import numpy as np
from scipy.integrate import solve_ivp
from scipy.optimize import fsolve
import matplotlib.pyplot as plt

class ShootingMethodSolver:
    def __init__(self, t0, tf, phi1_L, phi1_R, phi2_L, phi2_R, phi3_L, phi3_R, m, I_p):
        """
        Initialize the shooting method solver
        
        Parameters:
        - t0, tf: time boundaries
        - phi1_L, phi1_R: phi1 boundary conditions
        - phi2_L, phi2_R: phi2 boundary conditions  
        - phi3_L, phi3_R: phi3 boundary conditions
        - m: mass parameter
        - I_p: moment of inertia parameter
        """
        self.t0 = t0
        self.tf = tf
        self.phi1_L = phi1_L
        self.phi1_R = phi1_R
        self.phi2_L = phi2_L
        self.phi2_R = phi2_R
        self.phi3_L = phi3_L
        self.phi3_R = phi3_R
        self.m = m
        self.I_p = I_p
        
    def ode_system(self, t, y):
        """
        Convert the system to first-order ODEs
        y = [phi1, phi1', phi1'', phi1''', phi2, phi2', phi2'', phi2''', phi3, phi3', phi3'', phi3''']
        """
        phi1, phi1_t, phi1_tt, phi1_ttt = y[0], y[1], y[2], y[3]
        phi2, phi2_t, phi2_tt, phi2_ttt = y[4], y[5], y[6], y[7]
        phi3, phi3_t, phi3_tt, phi3_ttt = y[8], y[9], y[10], y[11]
        
        # Calculate fourth derivatives from the given equations
        # phi1''(t) = -phi3'(t)*sin(phi3(t)) + m*(phi3''(t)*sin(phi3(t)) + (phi3'(t))^2*cos(phi3(t)) + m*phi1''''(t))
        # Rearranging: phi1''''(t) = (phi1''(t) + phi3'(t)*sin(phi3(t)) - m*(phi3''(t)*sin(phi3(t)) + (phi3'(t))^2*cos(phi3(t)))) / (m^2)
        
        # phi2''(t) = phi3'(t)*cos(phi3(t)) - m*(phi3''(t)*cos(phi3(t)) - (phi3'(t))^2*sin(phi3(t)) - m*phi2''''(t))
        # Rearranging: phi2''''(t) = (phi2''(t) - phi3'(t)*cos(phi3(t)) + m*(phi3''(t)*cos(phi3(t)) - (phi3'(t))^2*sin(phi3(t)))) / (m^2)
        
        # phi3''(t) = phi1'(t)*sin(phi3(t)) - phi2'(t)*cos(phi3(t)) + m*(phi1''(t)*sin(phi3(t)) - phi2''(t)*cos(phi3(t)) + I_p^2*phi3''''(t)/m)
        # Rearranging: phi3''''(t) = m*(phi3''(t) - phi1'(t)*sin(phi3(t)) + phi2'(t)*cos(phi3(t)) - m*(phi1''(t)*sin(phi3(t)) - phi2''(t)*cos(phi3(t)))) / (I_p^2)
        
        sin_phi3 = np.sin(phi3)
        cos_phi3 = np.cos(phi3)
        
        # Calculate fourth derivatives
        phi1_tttt = (phi1_tt + phi3_t * sin_phi3 - self.m * (phi3_tt * sin_phi3 + phi3_t**2 * cos_phi3)) / (self.m**2)
        phi2_tttt = (phi2_tt - phi3_t * cos_phi3 + self.m * (phi3_tt * cos_phi3 - phi3_t**2 * sin_phi3)) / (self.m**2)
        phi3_tttt = self.m * (phi3_tt - phi1_t * sin_phi3 + phi2_t * cos_phi3 - self.m * (phi1_tt * sin_phi3 - phi2_tt * cos_phi3)) / (self.I_p**2)
        
        # Return derivatives
        dydt = [
            phi1_t,      # phi1' = phi1_t
            phi1_tt,     # phi1'' = phi1_tt
            phi1_ttt,    # phi1''' = phi1_ttt
            phi1_tttt,   # phi1'''' = phi1_tttt
            phi2_t,      # phi2' = phi2_t
            phi2_tt,     # phi2'' = phi2_tt
            phi2_ttt,    # phi2''' = phi2_ttt
            phi2_tttt,   # phi2'''' = phi2_tttt
            phi3_t,      # phi3' = phi3_t
            phi3_tt,     # phi3'' = phi3_tt
            phi3_ttt,    # phi3''' = phi3_ttt
            phi3_tttt    # phi3'''' = phi3_tttt
        ]
        
        return dydt
    
    def solve_with_initial_guess(self, initial_derivatives):
        """
        Solve the ODE with given initial derivatives
        initial_derivatives = [phi1'(t0), phi1''(t0), phi1'''(t0), phi2'(t0), phi2''(t0), phi2'''(t0), phi3'(t0), phi3''(t0), phi3'''(t0)]
        """
        # Initial conditions
        y0 = [
            self.phi1_L,  # phi1(t0)
            initial_derivatives[0],  # phi1'(t0)
            initial_derivatives[1],  # phi1''(t0)
            initial_derivatives[2],  # phi1'''(t0)
            self.phi2_L,  # phi2(t0)
            initial_derivatives[3],  # phi2'(t0)
            initial_derivatives[4],  # phi2''(t0)
            initial_derivatives[5],  # phi2'''(t0)
            self.phi3_L,  # phi3(t0)
            initial_derivatives[6],  # phi3'(t0)
            initial_derivatives[7],  # phi3''(t0)
            initial_derivatives[8],  # phi3'''(t0)
        ]
        
        # Solve the ODE
        sol = solve_ivp(self.ode_system, [self.t0, self.tf], y0, 
                       dense_output=True, rtol=1e-8, atol=1e-10)
        
        return sol
    
    def boundary_residual(self, initial_derivatives):
        """
        Calculate the residual for boundary conditions
        """
        sol = self.solve_with_initial_guess(initial_derivatives)
        
        if not sol.success:
            return np.array([1e6, 1e6, 1e6, 1e6, 1e6, 1e6, 1e6, 1e6, 1e6])
        
        # Get final values
        y_final = sol.y[:, -1]
        phi1_final = y_final[0]
        phi2_final = y_final[4]
        phi3_final = y_final[8]
        
        # Calculate residuals
        residuals = [
            phi1_final - self.phi1_R,  # phi1(tf) - phi1_R
            phi2_final - self.phi2_R,  # phi2(tf) - phi2_R
            phi3_final - self.phi3_R,  # phi3(tf) - phi3_R
            # Add additional constraints if needed
            0, 0, 0, 0, 0, 0  # Placeholder for additional constraints
        ]
        
        return np.array(residuals[:3])  # Only use the first 3 residuals
    
    def solve(self, initial_guess=None):
        """
        Solve the boundary value problem using shooting method
        """
        if initial_guess is None:
            # Default initial guess for derivatives
            initial_guess = [0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1]
        
        print("Starting shooting method...")
        print(f"Boundary conditions:")
        print(f"  phi1: {self.phi1_L} → {self.phi1_R}")
        print(f"  phi2: {self.phi2_L} → {self.phi2_R}")
        print(f"  phi3: {self.phi3_L} → {self.phi3_R}")
        print(f"Parameters: m={self.m}, I_p={self.I_p}")
        
        # Use only the first 3 elements for the 3 boundary conditions
        def residual_func(x):
            # Extend x to 9 elements if needed
            if len(x) == 3:
                extended_x = list(x) + [0.1] * 6  # Add default values for higher derivatives
            else:
                extended_x = x
            return self.boundary_residual(extended_x)
        
        # Solve using fsolve
        solution = fsolve(residual_func, initial_guess[:3], xtol=1e-8)
        
        # Get the final solution
        extended_solution = list(solution) + [0.1] * 6
        final_sol = self.solve_with_initial_guess(extended_solution)
        
        if final_sol.success:
            print("Shooting method converged successfully!")
            residual = self.boundary_residual(extended_solution)
            print(f"Final residual: {np.linalg.norm(residual):.2e}")
        else:
            print("Warning: ODE solver failed")
        
        return final_sol, solution
    
    def plot_solution(self, sol, save_filename=None):
        """
        Plot the solution
        """
        t_eval = np.linspace(self.t0, self.tf, 1000)
        y_eval = sol.sol(t_eval)
        
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        
        # Plot phi1, phi2, phi3
        axes[0,0].plot(t_eval, y_eval[0], 'r-', linewidth=2, label='phi1(t)')
        axes[0,0].plot(t_eval, y_eval[4], 'g-', linewidth=2, label='phi2(t)')
        axes[0,0].plot(t_eval, y_eval[8], 'b-', linewidth=2, label='phi3(t)')
        axes[0,0].set_xlabel('t')
        axes[0,0].set_ylabel('phi(t)')
        axes[0,0].set_title('Position Functions')
        axes[0,0].legend()
        axes[0,0].grid(True, alpha=0.3)
        
        # Plot trajectory
        axes[0,1].plot(y_eval[0], y_eval[4], 'r-', linewidth=2)
        axes[0,1].plot(self.phi1_L, self.phi2_L, 'go', markersize=8, label='Start')
        axes[0,1].plot(self.phi1_R, self.phi2_R, 'ro', markersize=8, label='End')
        axes[0,1].set_xlabel('phi1 (x)')
        axes[0,1].set_ylabel('phi2 (y)')
        axes[0,1].set_title('Trajectory')
        axes[0,1].legend()
        axes[0,1].grid(True, alpha=0.3)
        axes[0,1].axis('equal')
        
        # Plot first derivatives
        axes[1,0].plot(t_eval, y_eval[1], 'r-', linewidth=2, label="phi1'(t)")
        axes[1,0].plot(t_eval, y_eval[5], 'g-', linewidth=2, label="phi2'(t)")
        axes[1,0].plot(t_eval, y_eval[9], 'b-', linewidth=2, label="phi3'(t)")
        axes[1,0].set_xlabel('t')
        axes[1,0].set_ylabel("phi'(t)")
        axes[1,0].set_title('First Derivatives')
        axes[1,0].legend()
        axes[1,0].grid(True, alpha=0.3)
        
        # Plot second derivatives
        axes[1,1].plot(t_eval, y_eval[2], 'r-', linewidth=2, label="phi1''(t)")
        axes[1,1].plot(t_eval, y_eval[6], 'g-', linewidth=2, label="phi2''(t)")
        axes[1,1].plot(t_eval, y_eval[10], 'b-', linewidth=2, label="phi3''(t)")
        axes[1,1].set_xlabel('t')
        axes[1,1].set_ylabel("phi''(t)")
        axes[1,1].set_title('Second Derivatives')
        axes[1,1].legend()
        axes[1,1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_filename:
            plt.savefig(save_filename, dpi=300, bbox_inches='tight')
        
        plt.show()
        
        return fig

def main():
    """
    Example usage of the shooting method solver
    """
    # Parameters (modify these as needed)
    t0 = 0.0
    tf = 12.0
    
    # Boundary conditions
    phi1_L = 0.0
    phi1_R = 3.0
    phi2_L = 0.0
    phi2_R = -5.0
    phi3_L = 0.0
    phi3_R = 0.0
    
    # Physical parameters
    m = 0.1
    I_p = 0.1
    
    # Create solver
    solver = ShootingMethodSolver(t0, tf, phi1_L, phi1_R, phi2_L, phi2_R, phi3_L, phi3_R, m, I_p)
    
    # Solve
    sol, initial_derivatives = solver.solve()
    
    if sol.success:
        print(f"Optimal initial derivatives: {initial_derivatives}")
        
        # Plot solution
        solver.plot_solution(sol, 'shooting_method_solution.png')
        
        # Save results
        t_eval = np.linspace(t0, tf, 1000)
        y_eval = sol.sol(t_eval)
        
        # Save to files
        np.savetxt('shooting_phi1.txt', np.column_stack([t_eval, y_eval[0]]), fmt='%.6e')
        np.savetxt('shooting_phi2.txt', np.column_stack([t_eval, y_eval[4]]), fmt='%.6e')
        np.savetxt('shooting_phi3.txt', np.column_stack([t_eval, y_eval[8]]), fmt='%.6e')
        
        print("Results saved to shooting_phi*.txt files")
    else:
        print("Failed to find solution")

if __name__ == "__main__":
    main()
