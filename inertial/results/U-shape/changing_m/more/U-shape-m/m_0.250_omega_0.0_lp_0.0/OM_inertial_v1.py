##################
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
import numpy as np
import os
import pickle
import matplotlib.pyplot as plt
import time
from scipy.integrate import simpson
import numpy as np

class neural_net(nn.Module):
    def __init__(self):
        super(neural_net, self).__init__()
        self.hidden_dim = 30

        self.layer1 = nn.Linear(1, self.hidden_dim)
        self.layer2 = nn.Linear(self.hidden_dim, self.hidden_dim)
        self.layer3 = nn.Linear(self.hidden_dim, 1)
        # linear connection 1 -> 20, 20 -> 20, 20 -> 1 gives [1, 20, 20, 1]

        # Apply Xavier initialization (This initialization makes the output variance in a norm range)
        nn.init.xavier_uniform_(self.layer1.weight)
        nn.init.xavier_uniform_(self.layer2.weight)
        nn.init.xavier_uniform_(self.layer3.weight)

    def forward(self, t):
        x = torch.tanh(self.layer1(t))
        x = torch.tanh(self.layer2(x))
        x = self.layer3(x)
        return x

class neural_net_theta(nn.Module):
    def __init__(self):
        super(neural_net_theta, self).__init__()
        self.hidden_dim = 30

        self.layer1 = nn.Linear(1, self.hidden_dim)
        self.layer2 = nn.Linear(self.hidden_dim, self.hidden_dim)
        self.layer3 = nn.Linear(self.hidden_dim, 1)

        # Apply Xavier initialization
        nn.init.xavier_uniform_(self.layer1.weight)
        nn.init.xavier_uniform_(self.layer2.weight)
        nn.init.xavier_uniform_(self.layer3.weight)

    def forward(self, t):
        x = torch.tanh(self.layer1(t))  #
        x = self.layer2(x)
        x = self.layer3(x)
        theta = x
        return theta
# Note that there are different ways for handling output representing "orientation"
# This choice here is just a simple one but might not be general enough for more complicated cases.


# instantiates networks phi only (no g networks needed)
neural_net_phi1 = neural_net()
neural_net_phi2 = neural_net()
neural_net_phi3 = neural_net_theta()

# so hereafter, calling  neural_net_phi1(t) will give output corresponding to their phi1 (the x_i(t)), et cetera.

##################

# First, this is for the drift terms
def potential(x,y,theta):
    U_x=-torch.cos(theta)
    U_y=-torch.sin(theta)
    U_theta = 0
    return U_x,U_y, U_theta

# now we write the function that will output some quantities we will need for calculating loss
# remember that minimizing the loss will lead to correct dynamical equations and BCs,
# so here can be consider the place where we determine the differential equations and the OM integral value (g)
def net_ode_phi(t): # t is passed in later as a vector t \in [0, terminal time] with shape [batch_size, 1], (batch_size is the size of the t vector here)
    phi1 = neural_net_phi1(t)
    phi2 = neural_net_phi2(t)
    phi3 = neural_net_phi3(t)

    phi1_t = torch.autograd.grad(outputs=phi1, inputs=t, grad_outputs=torch.ones_like(phi1), create_graph=True)[0]
    phi2_t = torch.autograd.grad(outputs=phi2, inputs=t, grad_outputs=torch.ones_like(phi2), create_graph=True)[0]
    phi3_t = torch.autograd.grad(outputs=phi3, inputs=t, grad_outputs=torch.ones_like(phi3), create_graph=True)[0]

    phi1_tt = torch.autograd.grad(outputs=phi1_t, inputs=t, grad_outputs=torch.ones_like(phi1_t), create_graph=True)[0]
    phi2_tt = torch.autograd.grad(outputs=phi2_t, inputs=t, grad_outputs=torch.ones_like(phi2_t), create_graph=True)[0]
    phi3_tt = torch.autograd.grad(outputs=phi3_t, inputs=t, grad_outputs=torch.ones_like(phi3_t), create_graph=True)[0]

    U_xytheta = potential(phi1, phi2, phi3)

    m_tensor = torch.tensor(m, device=t.device)
    omega_tensor = torch.tensor(omega, device=t.device)
    I_p_tensor = torch.tensor(I_p, device=t.device)

    # om_integrand = (phi1_t + U_x + m*phi1'')^2 + (phi2_t + U_y + m*phi2'')^2 + (phi3_t + U_theta - omega + I_p*phi3'')^2
    om_integrand = ((phi1_t + U_xytheta[0] + m_tensor * phi1_tt)**2 +
                    (phi2_t + U_xytheta[1] + m_tensor * phi2_tt)**2 +
                    (phi3_t + U_xytheta[2] - omega_tensor + I_p_tensor * phi3_tt)**2)

    return phi1, phi2, phi3, om_integrand, phi1_t, phi2_t, phi3_t, phi1_tt, phi2_tt, phi3_tt
"""
Return 0, 1, 2: phi(t_0 to t_f), size = [batch_size, 1]
Return 3: OM integrand = (phi1_t + U_x + m*phi1'')^2 + (phi2_t + U_y + m*phi2'')^2 + (phi3_t + U_theta - omega + I_p*phi3'')^2
Return 4, 5, 6: phi_t derivatives (dφ₁/dt, dφ₂/dt, dφ₃/dt), size = [batch_size, 1]
Return 7, 8, 9: phi_tt second derivatives (d²φ₁/dt², d²φ₂/dt², d²φ₃/dt²), size = [batch_size, 1]
"""
##################
# parameters
t0=0
t1=12
Nt=12000
dt=(t1-t0)/Nt

with open('para.txt', 'r') as f:
    lines = f.readlines()
    m = float(lines[0].strip())      # Mass parameter
    omega = float(lines[1].strip())  # Omega parameter
    I_p = float(lines[2].strip())    # Moment of inertia parameter


# here you create the time vector, which we will input all these times into the networks in each iteration again and again
vect_tf = torch.linspace(t0, t1, Nt + 1).view(-1, 1).float()#.to(device)
vect_tf.requires_grad_(True) # IMPORTANT
# Note that we need to call requires_grad_(True) to construct the "computational graph" for later loss backwards.
# hint (not important for here): in PyTorch, when there is an "_" after the function, like requires_grad"_", means that it is an "in-place operator"

##################
# Boundary constraints
phi1_L = 0
phi1_R = 5
phi2_L = 0
phi2_R = 0
phi3_L = 0
phi3_R = 2 * np.pi * 0

"""
TensorFlow uses a static graph approach,
while PyTorch builds the computational graph dynamically during each forward pass
"""

##################
loss_g_record = []
loss_b_record=[]
loss_path_constraint_record=[]
savedir='re'

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
# cuda is for gpu, here device will = cpu if you run it on M1 chips
# But device will = cuda if you run your code on google colab and use T4 GPU

# Move models to the device
neural_net_phi1.to(device)
neural_net_phi2.to(device)
neural_net_phi3.to(device)


# Move input tensor to the device (! since you need to calculate v_t)
vect_tf = vect_tf.to(device)

# Optimizer
"""
TensorFlow's optimizer is bind with the loss, it is different from PyTorch as well
"""
# optimizer update the networks using the gradient information after backward.
# Adam is a very popular optimizer.
optimizer = optim.Adam(
    list(neural_net_phi1.parameters()) +
    list(neural_net_phi2.parameters()) +
    list(neural_net_phi3.parameters()),
    lr=1e-4 # learning rate, this is an important hyperparameter that you can finetune.
)

# Learning rate scheduler: multiply the learning rate by 0.5 every 15,000 steps
scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=20000, gamma = 0.5)

# Track minimum loss
min_loss = 1e16
min_OM_Value = 1e16
##################
# Ensure the directory exists
result_dir = './result/'
if not os.path.exists(result_dir):
    os.makedirs(result_dir)

start_time = time.time() # this is just to calculate the run time

for i in range(40001): # Increased boundary weight test
    optimizer.zero_grad() # Clear gradients recorded from the previous iteration

    # Forward pass. (Passing vect_tf to the networks and obtain some quantities and loss)
    phi1_outputs, phi2_outputs, phi3_outputs, f, phi1_t_outputs, phi2_t_outputs, phi3_t_outputs, phi1_tt_outputs, phi2_tt_outputs, phi3_tt_outputs = net_ode_phi(vect_tf)

    # OM integral: f is the integrand (phi1_t + U_x)^2 + (phi2_t + U_y)^2 + (phi3_t + U_theta)^2
    loss_g = dt * torch.sum(f)  # 这就是 OM 积分值
    # Extract boundary va
    phi1L_nn = phi1_outputs[0]  # First element (at t=t0)
    phi2L_nn = phi2_outputs[0]  # First element (at t=t0)
    phi3L_nn = phi3_outputs[0]

    phi1R_nn = phi1_outputs[-1]  # Last element (at t=t1)
    phi2R_nn = phi2_outputs[-1]  # Last element (at t=t1)
    phi3R_nn = phi3_outputs[-1]

    # Boundary loss
    loss_b = (
        torch.mean((phi1L_nn - phi1_L) ** 2) +
        torch.mean((phi1R_nn - phi1_R) ** 2) +
        torch.mean((phi2L_nn - phi2_L) ** 2) +
        torch.mean((phi2R_nn - phi2_R) ** 2) +
        torch.mean((phi3L_nn - phi3_L) ** 2) +
        torch.mean((phi3R_nn - phi3_R) ** 2)
    )

    # Path constraint losses for x boundaries
    phi1_upper_violation = torch.clamp(phi1_outputs - phi1_R, min=0)  # phi1 > phi1_R violations
    phi1_lower_violation = torch.clamp(phi1_L - phi1_outputs, min=0)  # phi1 < phi1_L violations

    loss_path_upper = torch.mean(phi1_upper_violation ** 2)  # Penalty for phi1 > phi1_R
    loss_path_lower = torch.mean(phi1_lower_violation ** 2)  # Penalty for phi1 < phi1_L
    loss_path_constraint = loss_path_upper + loss_path_lower

    loss = loss_g + 100 * loss_b + 0 * loss_path_constraint # added phi1 boundary constraint penalty
    # Backpropagation and optimizer step
    loss.backward()
    optimizer.step()
    scheduler.step()

    """
    Note that the save and print and later plot functions in this version record only for the phi1 and phi2 here, 
    for the orientation (phi3 and g3), we can add it of course (but I am lazy for now lol).
    """
    # Save and print results every 1000 iterations
    if i % 100 == 0:
        phi_t0 = (
            neural_net_phi1(vect_tf).detach().cpu().numpy(),
            neural_net_phi2(vect_tf).detach().cpu().numpy(),
            neural_net_phi3(vect_tf).detach().cpu().numpy()
        )
        loss_g_result = loss_g.item()
        # Removed loss_phi_result
        loss_b_result = loss_b.item()
        loss_path_constraint_result = loss_path_constraint.item()
        # Removed loss_dot_boundary_result

        loss_g_record.append(loss_g_result)
        loss_b_record.append(loss_b_result)
        loss_path_constraint_record.append(loss_path_constraint_result)

        phi1L_np = phi1L_nn.item()
        phi1R_np = phi1R_nn.item()
        phi2L_np = phi2L_nn.item()
        phi2R_np = phi2R_nn.item()
        phi3L_np = phi3L_nn.item()
        phi3R_np = phi3R_nn.item()
        # Removed computation of differential equation residuals

        if loss.item() < min_loss:
            min_loss = loss.item()
            min_OM_Value = loss_g.item()  # Record minimum OM value
            phi_opt = (
                neural_net_phi1(vect_tf).detach().cpu().numpy(),
                neural_net_phi2(vect_tf).detach().cpu().numpy(),
                neural_net_phi3(vect_tf).detach().cpu().numpy()
            )
            i_opt = i

            # Save boundary condition velocity and acceleration information for later output
            # Extract values at t=0 (first element) and t=t1 (last element)
            opt_phi1_t_0 = phi1_t_outputs[0].detach().cpu().numpy().item()
            opt_phi2_t_0 = phi2_t_outputs[0].detach().cpu().numpy().item()
            opt_phi3_t_0 = phi3_t_outputs[0].detach().cpu().numpy().item()
            opt_phi1_tt_0 = phi1_tt_outputs[0].detach().cpu().numpy().item()
            opt_phi2_tt_0 = phi2_tt_outputs[0].detach().cpu().numpy().item()
            opt_phi3_tt_0 = phi3_tt_outputs[0].detach().cpu().numpy().item()

            opt_phi1_t_t1 = phi1_t_outputs[-1].detach().cpu().numpy().item()
            opt_phi2_t_t1 = phi2_t_outputs[-1].detach().cpu().numpy().item()
            opt_phi3_t_t1 = phi3_t_outputs[-1].detach().cpu().numpy().item()
            opt_phi1_tt_t1 = phi1_tt_outputs[-1].detach().cpu().numpy().item()
            opt_phi2_tt_t1 = phi2_tt_outputs[-1].detach().cpu().numpy().item()
            opt_phi3_tt_t1 = phi3_tt_outputs[-1].detach().cpu().numpy().item()

        current_lr = scheduler.get_last_lr()[0]
        # Format the log message
        log_message = (f'{i:5d} LOSS:{loss.item():8.2e} OM_sum:{loss_g_result:8.5e} '
                      f'BCs:{loss_b_result:8.2e} Path:{loss_path_constraint_result:8.2e} '
                      f'BC_X:{phi1L_np:8.2e} {phi1R_np:8.2e} BC_Y:{phi2L_np:8.2e} {phi2R_np:8.2e} BC_theta:{phi3L_np:8.2e} {phi3R_np:8.2e} LR:{current_lr:8.2e}')

        # Print to console
        print(log_message)

        # Write to log file
        with open('output.log', 'a') as log_file:
            log_file.write(log_message + '\n')
    # Save model and loss records every 50000 iterations
    if i % 50000 == 0:
        torch.save({
            'weights_phi1': neural_net_phi1.state_dict(),
            'weights_phi2': neural_net_phi2.state_dict(),
            'weights_phi3': neural_net_phi3.state_dict(),
        }, f'./result/hyper_{i}.pth')

        np.savetxt(f'./result/loss_g_T_{t1}_iter_{i}.txt', np.array(loss_g_record), fmt='%10.5e')
        np.savetxt(f'./result/loss_b_T_{t1}_iter_{i}.txt', np.array(loss_b_record), fmt='%10.5e')
        np.savetxt(f'./result/loss_path_constraint_T_{t1}_iter_{i}.txt', np.array(loss_path_constraint_record), fmt='%10.5e')

        # Optionally save additional data in a pickle file
        sample_list = {
            "weights_phi1": neural_net_phi1.state_dict(),
            "weights_phi2": neural_net_phi2.state_dict(),
            "weights_phi3": neural_net_phi3.state_dict(),
        }
        file_name = f'./result/hyper_{i}.pkl'
        with open(file_name, "wb") as open_file:
            pickle.dump(sample_list, open_file)


# End the timer and print elapsed time
end_time = time.time()
elapsed_time = end_time - start_time
print(f'Total run time: {elapsed_time:.2f} seconds')

##################
# Assuming you have already saved the model states using `torch.save()`
# Extract weights and biases from the neural networks
weights_phi1_np = [param.detach().cpu().numpy() for param in neural_net_phi1.parameters()]
biases_phi1_np = [param.detach().cpu().numpy() for param in neural_net_phi1.parameters()]
weights_phi2_np = [param.detach().cpu().numpy() for param in neural_net_phi2.parameters()]
biases_phi2_np = [param.detach().cpu().numpy() for param in neural_net_phi2.parameters()]

# Save the weights and biases to text files
np.savetxt('./result/weights_phi1_0.txt', weights_phi1_np[0], fmt='%10.5e')
np.savetxt('./result/weights_phi1_1.txt', weights_phi1_np[1], fmt='%10.5e')
np.savetxt('./result/weights_phi1_2.txt', weights_phi1_np[2], fmt='%10.5e')
np.savetxt('./result/biases_phi1_0.txt', biases_phi1_np[0], fmt='%10.5e')
np.savetxt('./result/biases_phi1_1.txt', biases_phi1_np[1], fmt='%10.5e')
np.savetxt('./result/biases_phi1_2.txt', biases_phi1_np[2], fmt='%10.5e')

np.savetxt('./result/weights_phi2_0.txt', weights_phi2_np[0], fmt='%10.5e')
np.savetxt('./result/weights_phi2_1.txt', weights_phi2_np[1], fmt='%10.5e')
np.savetxt('./result/weights_phi2_2.txt', weights_phi2_np[2], fmt='%10.5e')
np.savetxt('./result/biases_phi2_0.txt', biases_phi2_np[0], fmt='%10.5e')
np.savetxt('./result/biases_phi2_1.txt', biases_phi2_np[1], fmt='%10.5e')
np.savetxt('./result/biases_phi2_2.txt', biases_phi2_np[2], fmt='%10.5e')
# Save data with time column: [t, phi1], [t, phi2], [t, phi3]
t_data = vect_tf.detach().cpu().numpy().flatten()

# Combine time with phi values for two-column output
x_data = np.column_stack((t_data, phi_opt[0]))
y_data = np.column_stack((t_data, phi_opt[1]))
theta_data = np.column_stack((t_data, phi_opt[2]))
xy_data= np.column_stack((phi_opt[0], phi_opt[1]))
# Save with headers for clarity
np.savetxt('./result/x.txt', x_data, fmt='%10.5e')
np.savetxt('./result/y.txt', y_data, fmt='%10.5e')
np.savetxt('./result/theta.txt', theta_data, fmt='%10.5e')
np.savetxt('./result/XY_data.txt', xy_data, fmt='%10.5e')
np.savetxt('./result/OM_value.txt', [omega, min_OM_Value], fmt='%10.5e')

# Save boundary condition velocity and acceleration information
bc_data = [
    ['Variable', 'At t=0', 'At t=t1'],
    ['phi1_t (dx/dt)', f'{opt_phi1_t_0:.6e}', f'{opt_phi1_t_t1:.6e}'],
    ['phi2_t (dy/dt)', f'{opt_phi2_t_0:.6e}', f'{opt_phi2_t_t1:.6e}'],
    ['phi3_t (dtheta/dt)', f'{opt_phi3_t_0:.6e}', f'{opt_phi3_t_t1:.6e}'],
    ['phi1_tt (d²x/dt²)', f'{opt_phi1_tt_0:.6e}', f'{opt_phi1_tt_t1:.6e}'],
    ['phi2_tt (d²y/dt²)', f'{opt_phi2_tt_0:.6e}', f'{opt_phi2_tt_t1:.6e}'],
    ['phi3_tt (d²theta/dt²)', f'{opt_phi3_tt_0:.6e}', f'{opt_phi3_tt_t1:.6e}']
]
with open('./result/BC_Velocity_Output.txt', 'w') as f:
    for row in bc_data:
        f.write(f"{row[0]:<20} {row[1]:<15} {row[2]:<15}\n")

print(f"Minimum OM value = {min_OM_Value:.6f}")

##################
vect=np.linspace(t0,t1,Nt+1)[:,None]

plt.rcParams['mathtext.default'] = 'regular'
fig = plt.figure()
plt.plot(phi_opt[0], phi_opt[1], 'r', label=r'$\phi_{NN}$')
plt.xlabel(r'$\bar{x}$')
plt.ylabel(r'$\bar{y}$')
plt.xlim(0, phi1_R)  # x轴范围
plt.ylim(0, 8)  # y轴范围
plt.gca().set_aspect(1)
plt.legend()
plt.show()
fig.savefig('tra.png')

fig=  plt.figure()
plt.plot(vect,phi_opt[0],'r', label=r'$x$' )
plt.plot(vect,phi_opt[1],'g', label=r'$y$' )
plt.plot(vect,phi_opt[2],'b', label=r'$\theta$' )
plt.xlabel(r'$t$')
plt.ylabel(r'$(x, y, $\theta$))$')
plt.legend()
plt.show()
fig.savefig('tra2.png')
##################
fig=  plt.figure()
plt.plot(vect,phi_opt[0],'r', label=r'$\phi_{NN}$' )
plt.xlabel(r'$t$')
plt.ylabel(r'$x$')
plt.legend()
plt.show()
fig.savefig('tra3.png')
##################
fig=  plt.figure()
plt.plot(vect,phi_opt[1],'b', label=r'$\phi_{NN}$' )
plt.xlabel(r'$t$')
plt.ylabel(r'$y$')
plt.legend()
plt.show()
fig.savefig('tra4.png')
##################
fig=  plt.figure()
plt.plot(vect,phi_opt[2],'m', label=r'$\phi_{NN}$' )
plt.xlabel(r'$t$')
plt.ylabel(r'$\theta$')
plt.legend()
plt.show()
fig.savefig('tra5.png')


# Loss curves visualization
# Create iteration array for x-axis (every 1000 iterations)
iterations = np.arange(0, len(loss_g_record)) * 1000

# All losses comparison
fig = plt.figure(figsize=(10, 6))

plt.semilogy(iterations, loss_g_record, 'r-', linewidth=2, label='OM Integral')
plt.semilogy(iterations, loss_b_record, 'orange', linewidth=2, label='Boundary')
plt.xlabel('Training Iterations', fontsize=12)
plt.ylabel('Loss (log scale)', fontsize=12)
#plt.title('All Loss Components Comparison', fontsize=14)
plt.legend(fontsize=11)
plt.grid(True, alpha=0.3)
plt.tight_layout()
plt.show()
fig.savefig('loss_curves.png', dpi=150, bbox_inches='tight')
