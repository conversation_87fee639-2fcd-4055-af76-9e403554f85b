#!/usr/bin/env python3

import os
import glob
import numpy as np

def collect_parameter_and_OM_data():
    data_list = []
    subdirs = [d for d in os.listdir('.') if os.path.isdir(d)]
    for subdir in subdirs:
        try:
            # Check for para.txt in subdirectory
            para_file = os.path.join(subdir, 'para.txt')
            if not os.path.exists(para_file):
                print(f"Warning: {para_file} not found, skipping {subdir}")
                continue
            
            # Read para.txt
            with open(para_file, 'r') as f:
                para_lines = f.readlines()
            
            if len(para_lines) < 3:
                print(f"Warning: {para_file} has less than 3 lines, skipping {subdir}")
                continue
            
            # Extract m, omega, I_p values
            m_value = float(para_lines[0].strip())
            omega_value = float(para_lines[1].strip())
            I_p_value = float(para_lines[2].strip())
            
            # Find OM_value.txt in subdirectories of current subdir
            om_pattern = os.path.join(subdir, "**/OM_value.txt")
            om_files = glob.glob(om_pattern, recursive=True)
            
            if not om_files:
                print(f"Warning: No OM_value.txt found in {subdir}, skipping")
                continue
            
            # Use the first OM_value.txt found
            om_file = om_files[0]
            
            # Read OM_value.txt
            with open(om_file, 'r') as f:
                om_lines = f.readlines()
            
            if len(om_lines) < 2:
                print(f"Warning: {om_file} has less than 2 lines, skipping {subdir}")
                continue
            
            # Extract OM value (second line)
            OM_value = float(om_lines[1].strip())
            
            data_list.append((m_value, omega_value, I_p_value, OM_value, subdir))
            print(f"Read {subdir}: m={m_value:.6e}, omega={omega_value:.6e}, I_p={I_p_value:.6e}, OM={OM_value:.6e}")
            
        except Exception as e:
            print(f"Error processing {subdir}: {e}")
            continue
    
    return data_list

def save_results(data_list, output_file='results.txt'):
    """
    Sort data by m value and save to results.txt
    Format: m omega I_p OM (space separated)
    """
    if not data_list:
        print("No data to save!")
        return
    
    # Sort by m value (first element of tuple)
    sorted_data = sorted(data_list, key=lambda x: x[0])
    
    print(f"\nSaving {len(sorted_data)} entries to {output_file}")
    
    with open(output_file, 'w') as f:
        # Write data
        for m_value, omega_value, I_p_value, OM_value, subdir in sorted_data:
            f.write(f"{m_value:.6e} {omega_value:.6e} {I_p_value:.6e} {OM_value:.6e}\n")
    
    print(f"Results saved to {output_file}")


def plot_results(data_list, save_plot=True):
    """
    Plot parameter relationships
    """
    try:
        import matplotlib.pyplot as plt
        
        if not data_list:
            print("No data to plot!")
            return
        
        # Sort by m value
        sorted_data = sorted(data_list, key=lambda x: x[0])
        
        m_values = [data[0] for data in sorted_data]
        omega_values = [data[1] for data in sorted_data]
        I_p_values = [data[2] for data in sorted_data]
        OM_values = [data[3] for data in sorted_data]
        
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        
        # Plot OM vs m
        axes[0,0].plot(m_values, OM_values, 'bo-', linewidth=2, markersize=4)
        axes[0,0].set_xlabel('m value')
        axes[0,0].set_ylabel('OM value')
        axes[0,0].set_title('OM vs m')
        axes[0,0].grid(True, alpha=0.3)
        axes[0,0].ticklabel_format(style='scientific', axis='both', scilimits=(-3,3))
        
        # Plot OM vs omega
        axes[0,1].plot(omega_values, OM_values, 'ro-', linewidth=2, markersize=4)
        axes[0,1].set_xlabel('omega value')
        axes[0,1].set_ylabel('OM value')
        axes[0,1].set_title('OM vs omega')
        axes[0,1].grid(True, alpha=0.3)
        axes[0,1].ticklabel_format(style='scientific', axis='both', scilimits=(-3,3))
        
        # Plot OM vs I_p
        axes[1,0].plot(I_p_values, OM_values, 'go-', linewidth=2, markersize=4)
        axes[1,0].set_xlabel('I_p value')
        axes[1,0].set_ylabel('OM value')
        axes[1,0].set_title('OM vs I_p')
        axes[1,0].grid(True, alpha=0.3)
        axes[1,0].ticklabel_format(style='scientific', axis='both', scilimits=(-3,3))
        
        # 3D scatter plot (m, omega, OM)
        from mpl_toolkits.mplot3d import Axes3D
        ax = fig.add_subplot(224, projection='3d')
        scatter = ax.scatter(m_values, omega_values, OM_values, c=OM_values, cmap='viridis', s=50)
        ax.set_xlabel('m value')
        ax.set_ylabel('omega value')
        ax.set_zlabel('OM value')
        ax.set_title('3D: m, omega, OM')
        plt.colorbar(scatter, ax=ax, shrink=0.5)
        
        plt.tight_layout()
        
        if save_plot:
            plt.savefig('parameter_OM_analysis.png', dpi=300, bbox_inches='tight')
            print("Plot saved to parameter_OM_analysis.png")
        
        plt.show()
        
    except ImportError:
        print("Matplotlib not available, skipping plot generation")

def main():

    # Get current directory
    current_dir = os.getcwd()
    # Collect data from all subdirectories
    data_list = collect_parameter_and_OM_data()
    
    if not data_list:
        print("No valid data found!")
        return
    
    # Save main results file
    save_results(data_list, 'results.txt')

    plot_results(data_list, save_plot=True)

if __name__ == "__main__":
    main()
