##################
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
import numpy as np
import os
import pickle
import matplotlib.pyplot as plt
import time
from scipy.integrate import simpson
import numpy as np

# 添加了4阶积分的代码
def scipy_precision_integration(f, t0, t1):

    f_flat = f.flatten()
    n = len(f_flat)

    if n == 1:
        return (t1 - t0) * f_flat[0]
    elif n == 2:
        # Trapezoidal rule for 2 points
        return (t1 - t0) * (f_flat[0] + f_flat[1]) / 2

    # Convert to numpy for scipy computation
    f_np = f_flat.detach().cpu().numpy()

    # Create time points from t0 to t1
    t_np = np.linspace(t0, t1, n)

    # Use scipy's Simpson integration (4th order accuracy)
    # This automatically handles the integration limits t0 to t1
    result = simpson(f_np, x=t_np)

    return torch.tensor(result, device=f.device, dtype=f.dtype)

##################
class neural_net(nn.Module):
    def __init__(self):
        super(neural_net, self).__init__()
        self.hidden_dim = 30

        self.layer1 = nn.Linear(1, self.hidden_dim)
        self.layer2 = nn.Linear(self.hidden_dim, self.hidden_dim)
        self.layer3 = nn.Linear(self.hidden_dim, 1)
        # linear connection 1 -> 20, 20 -> 20, 20 -> 1 gives [1, 20, 20, 1]

        # Apply Xavier initialization (This initialization makes the output variance in a norm range)
        nn.init.xavier_uniform_(self.layer1.weight)
        nn.init.xavier_uniform_(self.layer2.weight)
        nn.init.xavier_uniform_(self.layer3.weight)

    def forward(self, t):
        x = torch.tanh(self.layer1(t))
        x = torch.tanh(self.layer2(x))
        x = self.layer3(x)
        return x

class neural_net_theta(nn.Module):
    def __init__(self):
        super(neural_net_theta, self).__init__()
        self.hidden_dim = 30

        self.layer1 = nn.Linear(1, self.hidden_dim)
        self.layer2 = nn.Linear(self.hidden_dim, self.hidden_dim)
        self.layer3 = nn.Linear(self.hidden_dim, 1)

        # Apply Xavier initialization
        nn.init.xavier_uniform_(self.layer1.weight)
        nn.init.xavier_uniform_(self.layer2.weight)
        nn.init.xavier_uniform_(self.layer3.weight)

    def forward(self, t):
        x = torch.tanh(self.layer1(t))  #
        x = self.layer2(x)
        x = self.layer3(x)
        theta = x
        return theta
# the difference here is the output layer passed through tanh then multiplied by pi before output
# Note that there are different ways for handling output representing "orientation"
# This choice here is just a simple one but might not be general enough for more complicated cases.


# instantiates networks phi only (no g networks needed)
neural_net_phi1 = neural_net()
neural_net_phi2 = neural_net()
neural_net_phi3 = neural_net_theta()

# so hereafter, calling  neural_net_phi1(t) will give output corresponding to their phi1 (the x_i(t)), et cetera.

##################

# First, this is for the drift terms
def potential(x,y,theta):
    U_x=-torch.cos(theta)
    U_y=-torch.sin(theta)
    U_theta = 0
    return U_x,U_y, U_theta

# now we write the function that will output some quantities we will need for calculating loss
# remember that minimizing the loss will lead to correct dynamical equations and BCs,
# so here can be consider the place where we determine the differential equations and the OM integral value (g)
def net_ode_phi(t): # t is passed in later as a vector t \in [0, terminal time] with shape [batch_size, 1], (batch_size is the size of the t vector here)
    phi1 = neural_net_phi1(t)
    phi2 = neural_net_phi2(t)
    phi3 = neural_net_phi3(t)

    phi1_t = torch.autograd.grad(outputs=phi1, inputs=t, grad_outputs=torch.ones_like(phi1), create_graph=True)[0]
    phi2_t = torch.autograd.grad(outputs=phi2, inputs=t, grad_outputs=torch.ones_like(phi2), create_graph=True)[0]
    phi3_t = torch.autograd.grad(outputs=phi3, inputs=t, grad_outputs=torch.ones_like(phi3), create_graph=True)[0]

    U_xytheta = potential(phi1, phi2, phi3)

    # Calculate OM integral integrand: (phi1_t + U_x)^2 + (phi2_t + U_y)^2 + (phi3_t + U_theta)^2
    om_integrand = (phi1_t + U_xytheta[0])**2 + (phi2_t + U_xytheta[1])**2 + (phi3_t + U_xytheta[2])**2

    return phi1, phi2, phi3, om_integrand, phi1_t, phi2_t, phi3_t
"""
Return 0, 1, 2: phi(t_0 to t_f), size = [batch_size, 1]
Return 3: OM integrand = (phi1_t + U_x)^2 + (phi2_t + U_y)^2 + (phi3_t + U_theta)^2
Return 4, 5, 6: phi_t derivatives (dφ₁/dt, dφ₂/dt, dφ₃/dt), size = [batch_size, 1]
"""
##################
# parameters
t0=0
t1=12
Nt=12000 #10000?
dt=(t1-t0)/Nt

# here you create the time vector, which we will input all these times into the networks in each iteration again and again
vect_tf = torch.linspace(t0, t1, Nt + 1).view(-1, 1).float()#.to(device)
vect_tf.requires_grad_(True) # IMPORTANT
# Note that we need to call requires_grad_(True) to construct the "computational graph" for later loss backwards.
# hint (not important for here): in PyTorch, when there is an "_" after the function, like requires_grad"_", means that it is an "in-place operator"

##################
# Boundary constraints
phi1_L = 0
phi1_R = 5
phi2_L = 0
phi2_R = 0
phi3_L = 0
phi3_R = 2 * np.pi * 0

"""
TensorFlow uses a static graph approach,
while PyTorch builds the computational graph dynamically during each forward pass
"""

##################
loss_g_record = []
loss_b_record=[]
savedir='re'

# On the other hand, carefully handle the device you are using (check later)
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
# cuda is for gpu, here device will = cpu if you run it on M1 chips
# But device will = cuda if you run your code on google colab and use T4 GPU

# Move models to the device
neural_net_phi1.to(device)
neural_net_phi2.to(device)
neural_net_phi3.to(device)


# Move input tensor to the device (! since you need to calculate v_t)
vect_tf = vect_tf.to(device)

# Removed device transfer for removed parameters

# Optimizer
"""
TensorFlow's optimizer is bind with the loss, it is different from PyTorch as well
"""
# optimizer update the networks using the gradient information after backward.
# Adam is a very popular optimizer.
optimizer = optim.Adam(
    list(neural_net_phi1.parameters()) +
    list(neural_net_phi2.parameters()) +
    list(neural_net_phi3.parameters()),
    lr=1e-4 # learning rate, this is an important hyperparameter that you can finetune.
)

# 学习率调度器：每15000步将学习率乘以0.5
scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=10000, gamma = 0.5)

# Track minimum loss
min_loss = 1e16
##################
# Ensure the directory exists
result_dir = './result/'
if not os.path.exists(result_dir):
    os.makedirs(result_dir)

start_time = time.time() # this is just to calculate the run time

for i in range(30001): # Increased boundary weight test
    optimizer.zero_grad() # Clear gradients recorded from the previous iteration

    # Forward pass. (Passing vect_tf to the networks and obtain some quantities and loss)
    phi1_outputs, phi2_outputs, phi3_outputs, f, phi1_t_outputs, phi2_t_outputs, phi3_t_outputs = net_ode_phi(vect_tf)

    # OM integral: f is the integrand (phi1_t + U_x)^2 + (phi2_t + U_y)^2 + (phi3_t + U_theta)^2
    # Use scipy's Simpson rule for high-precision integration from t0 to t1
    loss_g_acc = scipy_precision_integration(f, t0, t1)
    loss_g = dt * torch.sum(f)  # 这就是 OM 积分值
   # loss_g = loss_g_acc
    # Extract boundary va
    phi1L_nn = phi1_outputs[0]  # First element (at t=t0)
    phi2L_nn = phi2_outputs[0]  # First element (at t=t0)
    phi3L_nn = phi3_outputs[0]

    phi1R_nn = phi1_outputs[-1]  # Last element (at t=t1)
    phi2R_nn = phi2_outputs[-1]  # Last element (at t=t1)
    phi3R_nn = phi3_outputs[-1]

    # Boundary loss
    loss_b = (
        torch.mean((phi1L_nn - phi1_L) ** 2) +
        torch.mean((phi1R_nn - phi1_R) ** 2) +
        torch.mean((phi2L_nn - phi2_L) ** 2) +
        torch.mean((phi2R_nn - phi2_R) ** 2) +
        torch.mean((phi3L_nn - phi3_L) ** 2) +
        torch.mean((phi3R_nn - phi3_R) ** 2)
    )

    # Removed derivative boundary loss (initial velocity constraints)


    # Removed path constraint losses

    # Removed loss_phi calculation
    loss = loss_g + 100 * loss_b # removed path constraint penalty and derivative boundary loss
    # Backpropagation and optimizer step
    loss.backward()
    optimizer.step()
    scheduler.step()

    """
    Note that the save and print and later plot functions in this version record only for the phi1 and phi2 here, 
    for the orientation (phi3 and g3), we can add it of course (but I am lazy for now lol).
    """
    # Save and print results every 1000 iterations
    if i % 100 == 0:
        phi_t0 = (
            neural_net_phi1(vect_tf).detach().cpu().numpy(),
            neural_net_phi2(vect_tf).detach().cpu().numpy(),
            neural_net_phi3(vect_tf).detach().cpu().numpy()
        )
        loss_g_result_acc = loss_g_acc.item()
        loss_g_result = loss_g.item()
        # Removed loss_phi_result
        loss_b_result = loss_b.item()
        # Removed loss_dot_boundary_result

        loss_g_record.append(loss_g_result)
        loss_b_record.append(loss_b_result)

        phi1L_np = phi1L_nn.item()
        phi1R_np = phi1R_nn.item()
        phi2L_np = phi2L_nn.item()
        phi2R_np = phi2R_nn.item()
        phi3L_np = phi3L_nn.item()
        phi3R_np = phi3R_nn.item()
        # Removed computation of differential equation residuals

        if loss.item() < min_loss:
            min_loss = loss.item()
            phi_opt = (
                neural_net_phi1(vect_tf).detach().cpu().numpy(),
                neural_net_phi2(vect_tf).detach().cpu().numpy(),
                neural_net_phi3(vect_tf).detach().cpu().numpy()
            )
            i_opt = i

        # 获取当前学习率
        current_lr = scheduler.get_last_lr()[0]
        print(f'{i:5d} LOSS:{loss.item():8.2e} OM_sum:{loss_g_result:8.5e} OM_Acc:{loss_g_result_acc:8.5e} '
              f'BCs:{loss_b_result:8.2e} '
              f'BC_X:{phi1L_np:8.2e} {phi1R_np:8.2e} BC_Y:{phi2L_np:8.2e} {phi2R_np:8.2e} BC_theta:{phi3L_np:8.2e} {phi3R_np:8.2e} LR:{current_lr:8.2e}')

    # Save model and loss records every 50000 iterations
    if i % 50000 == 0:
        torch.save({
            'weights_phi1': neural_net_phi1.state_dict(),
            'weights_phi2': neural_net_phi2.state_dict(),
            'weights_phi3': neural_net_phi3.state_dict(),
        }, f'./result/hyper_{i}.pth')

        np.savetxt(f'./result/loss_g_T_{t1}_iter_{i}.txt', np.array(loss_g_record), fmt='%10.5e')
        np.savetxt(f'./result/loss_b_T_{t1}_iter_{i}.txt', np.array(loss_b_record), fmt='%10.5e')

        # Optionally save additional data in a pickle file
        sample_list = {
            "weights_phi1": neural_net_phi1.state_dict(),
            "weights_phi2": neural_net_phi2.state_dict(),
            "weights_phi3": neural_net_phi3.state_dict(),
        }
        file_name = f'./result/hyper_{i}.pkl'
        with open(file_name, "wb") as open_file:
            pickle.dump(sample_list, open_file)


# End the timer and print elapsed time
end_time = time.time()
elapsed_time = end_time - start_time
print(f'Total run time: {elapsed_time:.2f} seconds')

##################
# Assuming you have already saved the model states using `torch.save()`
# Extract weights and biases from the neural networks
weights_phi1_np = [param.detach().cpu().numpy() for param in neural_net_phi1.parameters()]
biases_phi1_np = [param.detach().cpu().numpy() for param in neural_net_phi1.parameters()]
weights_phi2_np = [param.detach().cpu().numpy() for param in neural_net_phi2.parameters()]
biases_phi2_np = [param.detach().cpu().numpy() for param in neural_net_phi2.parameters()]

# Save the weights and biases to text files
np.savetxt('./result/weights_phi1_0.txt', weights_phi1_np[0], fmt='%10.5e')
np.savetxt('./result/weights_phi1_1.txt', weights_phi1_np[1], fmt='%10.5e')
np.savetxt('./result/weights_phi1_2.txt', weights_phi1_np[2], fmt='%10.5e')
np.savetxt('./result/biases_phi1_0.txt', biases_phi1_np[0], fmt='%10.5e')
np.savetxt('./result/biases_phi1_1.txt', biases_phi1_np[1], fmt='%10.5e')
np.savetxt('./result/biases_phi1_2.txt', biases_phi1_np[2], fmt='%10.5e')

np.savetxt('./result/weights_phi2_0.txt', weights_phi2_np[0], fmt='%10.5e')
np.savetxt('./result/weights_phi2_1.txt', weights_phi2_np[1], fmt='%10.5e')
np.savetxt('./result/weights_phi2_2.txt', weights_phi2_np[2], fmt='%10.5e')
np.savetxt('./result/biases_phi2_0.txt', biases_phi2_np[0], fmt='%10.5e')
np.savetxt('./result/biases_phi2_1.txt', biases_phi2_np[1], fmt='%10.5e')
np.savetxt('./result/biases_phi2_2.txt', biases_phi2_np[2], fmt='%10.5e')
# Save data with time column: [t, phi1], [t, phi2], [t, phi3]
t_data = vect_tf.detach().cpu().numpy().flatten()

# Combine time with phi values for two-column output
x_data = np.column_stack((t_data, phi_opt[0]))
y_data = np.column_stack((t_data, phi_opt[1]))
theta_data = np.column_stack((t_data, phi_opt[2]))

# Save with headers for clarity
np.savetxt('./result/x.txt', x_data, fmt='%10.5e')
np.savetxt('./result/y.txt', y_data, fmt='%10.5e')
np.savetxt('./result/theta.txt', theta_data, fmt='%10.5e')



##################
# 计算 (phi1_t + U_xytheta[0])^2 + (phi2_t + U_xytheta[1])^2 + (phi3_t + U_xytheta[2])^2 在 t0到t1 上的积分

# 首先定义时间向量
vect = np.linspace(t0, t1, Nt+1)[:, None]

# 计算时间导数
phi1_t_values = []
phi2_t_values = []
phi3_t_values = []

for i in range(len(vect)):
    t_point = torch.tensor([[vect[i][0]]], dtype=torch.float32, requires_grad=True).to(device)

    # 计算 phi 值
    phi1_val = neural_net_phi1(t_point)
    phi2_val = neural_net_phi2(t_point)
    phi3_val = neural_net_phi3(t_point)

    # 计算时间导数
    phi1_t = torch.autograd.grad(outputs=phi1_val, inputs=t_point,
                                grad_outputs=torch.ones_like(phi1_val),
                                create_graph=False)[0]
    phi2_t = torch.autograd.grad(outputs=phi2_val, inputs=t_point,
                                grad_outputs=torch.ones_like(phi2_val),
                                create_graph=False)[0]
    phi3_t = torch.autograd.grad(outputs=phi3_val, inputs=t_point,
                                grad_outputs=torch.ones_like(phi3_val),
                                create_graph=False)[0]

    phi1_t_values.append(phi1_t.detach().cpu().numpy()[0][0])
    phi2_t_values.append(phi2_t.detach().cpu().numpy()[0][0])
    phi3_t_values.append(phi3_t.detach().cpu().numpy()[0][0])

# 转换为numpy数组
phi1_t_array = np.array(phi1_t_values)
phi2_t_array = np.array(phi2_t_values)
phi3_t_array = np.array(phi3_t_values)

# 计算势能项 U_xytheta
U_x_values = -np.cos(phi_t0[2])  # U_x = -cos(phi3)
U_y_values = -np.sin(phi_t0[2])  # U_y = -sin(phi3)
U_theta_values = np.zeros_like(phi_t0[2])  # U_theta = 0

# 计算被积函数：(phi1_t + U_x)^2 + (phi2_t + U_y)^2 + (phi3_t + U_theta)^2
integrand = (phi1_t_array + U_x_values.flatten())**2 + (phi2_t_array + U_y_values.flatten())**2 + (phi3_t_array + U_theta_values.flatten())**2

# 使用梯形法则计算积分
dt = (t1 - t0) / (len(vect) - 1)
from scipy.integrate import trapezoid
integral_value = trapezoid(integrand, dx=dt)

print(f"∫[{t0},{t1}] [(dφ₁/dt + U_x)² + (dφ₂/dt + U_y)² + (dφ₃/dt + U_θ)²] dt = {integral_value:.6f}")

##################
vect=np.linspace(t0,t1,Nt+1)[:,None]

plt.rcParams['mathtext.default'] = 'regular'
fig = plt.figure()
plt.plot(phi_opt[0], phi_opt[1], 'r', label=r'$\phi_{NN}$')
plt.xlabel(r'$\bar{x}$')
plt.ylabel(r'$\bar{y}$')
plt.xlim(0, phi1_R)  # x轴范围
plt.ylim(0, 4)  # y轴范围
plt.gca().set_aspect(1)
plt.legend()
plt.show()

fig.savefig('tra.png')
fig=  plt.figure()
plt.plot(vect,phi_t0[0],'r', label=r'$x$' )
plt.plot(vect,phi_t0[1],'g', label=r'$y$' )
plt.plot(vect,phi_t0[2],'b', label=r'$\theta$' )
plt.xlabel(r'$t$')
plt.ylabel(r'$(x, y, $\theta$))$')
plt.legend()
plt.show()
fig.savefig('tra2.png')
##################
fig=  plt.figure()
plt.plot(vect,phi_opt[0],'r', label=r'$\phi_{NN}$' )
plt.xlabel(r'$t$')
plt.ylabel(r'$x$')
plt.legend()
plt.show()
fig.savefig('tra3.png')
##################
fig=  plt.figure()
plt.plot(vect,phi_opt[1],'b', label=r'$\phi_{NN}$' )
plt.xlabel(r'$t$')
plt.ylabel(r'$y$')
plt.legend()
plt.show()
fig.savefig('tra4.png')
##################
# ABP的θ坐标随时间变化
fig=  plt.figure()
plt.plot(vect,phi_opt[2],'m', label=r'$\phi_{NN}$' )
plt.xlabel(r'$t$')
plt.ylabel(r'$\theta$')
plt.legend()
plt.show()
fig.savefig('tra5.png')


# Loss curves visualization
# Create iteration array for x-axis (every 1000 iterations)
iterations = np.arange(0, len(loss_g_record)) * 1000

# All losses comparison
fig = plt.figure(figsize=(10, 6))

plt.semilogy(iterations, loss_g_record, 'r-', linewidth=2, label='OM Integral')
plt.semilogy(iterations, loss_b_record, 'orange', linewidth=2, label='Boundary')
plt.xlabel('Training Iterations', fontsize=12)
plt.ylabel('Loss (log scale)', fontsize=12)
#plt.title('All Loss Components Comparison', fontsize=14)
plt.legend(fontsize=11)
plt.grid(True, alpha=0.3)
plt.tight_layout()
plt.show()
fig.savefig('loss_curves.png', dpi=150, bbox_inches='tight')


