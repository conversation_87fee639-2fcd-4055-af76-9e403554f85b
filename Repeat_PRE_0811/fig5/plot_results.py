#!/usr/bin/env python3
"""
Plot results from the neural network optimization
Creates only the two specific plots from main-pc-v5-check-phase.py
"""

import numpy as np
import matplotlib.pyplot as plt
import os

def read_and_plot_results():
    """
    Read the saved result files and create the two specific plots
    """
    # Define file paths
    result_dir = './result'
    x_file = os.path.join(result_dir, 'x.txt')
    y_file = os.path.join(result_dir, 'y.txt')
    theta_file = os.path.join(result_dir, 'theta.txt')

    # Check if files exist
    files = [x_file, y_file, theta_file]
    file_names = ['x.txt', 'y.txt', 'theta.txt']

    for file_path, file_name in zip(files, file_names):
        if not os.path.exists(file_path):
            print(f"Warning: {file_name} not found at {file_path}")
            return

    try:
        # Read data files (assuming 2-column format: time, phi_value)
        print("Reading data files...")
        x_data = np.loadtxt(x_file)
        y_data = np.loadtxt(y_file)
        theta_data = np.loadtxt(theta_file)

        # Extract time and phi values
        if x_data.ndim == 2:
            # Two-column format: [time, phi]
            t_data, phi1 = x_data[:, 0], x_data[:, 1]
            _, phi2 = y_data[:, 0], y_data[:, 1]
            _, phi3 = theta_data[:, 0], theta_data[:, 1]
        else:
            phi1 = x_data
            phi2 = y_data
            phi3 = theta_data
            # Assume time from 0 to 12 with equal spacing
            n_points = len(phi1)
            t_data = np.linspace(0, 12, n_points)

        # Create the two specific plots
        create_trajectory_plot(phi1, phi2)
        create_time_series_plot(t_data, phi1, phi2, phi3)

    except Exception as e:
        print(f"Error reading or plotting data: {e}")

def create_trajectory_plot(phi1, phi2):
    """
    Plot 1: Trajectory plot (phi2 vs phi1)
    """
    # Set up matplotlib for LaTeX rendering
    plt.rcParams['mathtext.default'] = 'regular'

    fig = plt.figure()
    plt.plot(phi1, phi2, 'r', label=r'$\phi_{NN}$')
    plt.xlabel(r'$\bar{x}$')
    plt.ylabel(r'$\bar{y}$')

    # Set axis limits
    phi1_R = np.max(phi1)   # Adjust as needed
    plt.xlim(0, phi1_R)  # x-axis range
    plt.ylim(0, 4)       # y-axis range

    # Set axis tick intervals
    plt.xticks(np.arange(0, phi1_R, 1))  # x-axis ticks every 0.5, +0.5 to include phi1_R
    plt.yticks(np.arange(0, 4.1, 1))               # y-axis ticks every 1, range is already correct
    # Set equal total length for x and y axes
    ax = plt.gca()
    x_range = ax.get_xlim()[1] - ax.get_xlim()[0]  # Total x-axis length
    y_range = ax.get_ylim()[1] - ax.get_ylim()[0]  # Total y-axis length

    # Calculate aspect ratio to make total lengths equal
    aspect_ratio = y_range / x_range
    ax.set_aspect(aspect_ratio)
    plt.legend()
    plt.savefig('./result/trajectory_plot.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_time_series_plot(t_data, phi1, phi2, phi3):
    """
    Plot 2: Time series plot (all phi functions vs time)
    """
    # Create time vector (equivalent to vect=np.linspace(t0,t1,Nt+1)[:,None])
    vect = t_data.reshape(-1, 1)  # Make it column vector like in original code

    fig = plt.figure()
    plt.plot(vect, phi1, 'r', label=r'$x$')
    plt.plot(vect, phi2, 'g', label=r'$y$')
    plt.plot(vect, phi3, 'b', label=r'$\theta$')
    plt.xlabel(r'$t$')
    plt.ylabel(r'$(x, y, \theta)$')

    # Set axis tick intervals
    t_max = np.max(t_data)
    plt.xticks(np.arange(0, t_max + 1, 5))  # x-axis (time) ticks every 5 units, +1 to include t_max

    # Set y-axis ticks based on data range
    y_min = min(np.min(phi1), np.min(phi2), np.min(phi3))
    y_max = max(np.max(phi1), np.max(phi2), np.max(phi3))
    plt.yticks(np.arange(np.floor(y_min), np.ceil(y_max) + 1, 2))  # y-axis ticks every 2 units, +1 to include y_max

    plt.legend()
    plt.savefig('./result/tra2.png', dpi=300, bbox_inches='tight')
    plt.show()

if __name__ == "__main__":
    print("Neural Network Results Visualization")
    print("Creating the two specific plots...")
    print("="*50)

    # Create result directory if it doesn't exist
    os.makedirs('./result', exist_ok=True)

    # Read and plot results
    read_and_plot_results()
