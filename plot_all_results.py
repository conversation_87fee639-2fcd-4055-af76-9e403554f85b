#!/usr/bin/env python3
"""
Standalone script to read result files and regenerate all trajectory plots
Reads x.txt, y.txt, theta.txt from result directory and creates:
- tra.png: trajectory plot (y vs x)
- tra2.png: time series plot (x, y, theta vs time)
- tra3.png: trajectory plot with different settings
- tra4.png: x vs time plot
- tra5.png: theta vs time plot
"""

import numpy as np
import matplotlib.pyplot as plt
import os

def read_result_files():
    """
    Read the result files from result directory
    Returns time array and phi values
    """
    result_dir = './result'
    
    # Check if result directory exists
    if not os.path.exists(result_dir):
        print(f"Error: {result_dir} directory not found!")
        return None, None, None, None
    
    # File paths
    x_file = os.path.join(result_dir, 'x.txt')
    y_file = os.path.join(result_dir, 'y.txt')
    theta_file = os.path.join(result_dir, 'theta.txt')
    
    # Check if files exist
    files = [x_file, y_file, theta_file]
    file_names = ['x.txt', 'y.txt', 'theta.txt']
    
    for file_path, file_name in zip(files, file_names):
        if not os.path.exists(file_path):
            print(f"Error: {file_name} not found in {result_dir}")
            return None, None, None, None
    
    try:
        # Read data files
        print("Reading result files...")
        x_data = np.loadtxt(x_file)
        y_data = np.loadtxt(y_file)
        theta_data = np.loadtxt(theta_file)
        
        # Extract time and phi values
        if x_data.ndim == 2:
            # Two-column format: [time, phi]
            t_data = x_data[:, 0]
            phi1 = x_data[:, 1]
            phi2 = y_data[:, 1]
            phi3 = theta_data[:, 1]
        else:
            # Single column format: only phi values
            print("Single column format detected. Creating time array...")
            phi1 = x_data
            phi2 = y_data
            phi3 = theta_data
            # Assume time from 0 to 12 with equal spacing
            n_points = len(phi1)
            t_data = np.linspace(0, 12, n_points)
        
        print(f"Data loaded successfully:")
        print(f"  Time range: [{t_data[0]:.3f}, {t_data[-1]:.3f}]")
        print(f"  Number of points: {len(phi1)}")
        print(f"  phi1 (x) range: [{np.min(phi1):.6f}, {np.max(phi1):.6f}]")
        print(f"  phi2 (y) range: [{np.min(phi2):.6f}, {np.max(phi2):.6f}]")
        print(f"  phi3 (theta) range: [{np.min(phi3):.6f}, {np.max(phi3):.6f}]")
        
        return t_data, phi1, phi2, phi3
        
    except Exception as e:
        print(f"Error reading data files: {e}")
        return None, None, None, None

def plot_tra_png(phi1, phi2):
    """
    Generate tra.png: trajectory plot (phi2 vs phi1)
    """
    plt.rcParams['mathtext.default'] = 'regular'

    # Create figure and axis with forced square aspect ratio
    fig, ax = plt.subplots(figsize=(8, 8))
    ax.plot(phi1, phi2, 'r-', linewidth=2, label=r'$\phi_{NN}$')
    ax.set_xlabel(r'$\bar{x}$', fontsize=12)
    ax.set_ylabel(r'$\bar{y}$', fontsize=12)

    # Set axis limits
    phi1_max = np.max(phi1) * 1.1
    ax.set_xlim(0, phi1_max)
    ax.set_ylim(-6, 2)

    # Set axis tick intervals
    ax.set_xticks(np.arange(0, phi1_max + 0.5, 0.5))
    ax.set_yticks(np.arange(-6, 3, 1))

    # Set equal total length for x and y axes
    x_range = ax.get_xlim()[1] - ax.get_xlim()[0]  # Total x-axis length
    y_range = ax.get_ylim()[1] - ax.get_ylim()[0]  # Total y-axis length

    # Calculate aspect ratio to make total lengths equal
    aspect_ratio = y_range / x_range
    ax.set_aspect(aspect_ratio)
    ax.legend()
    ax.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig('tra.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("Generated tra.png")

def plot_tra2_png(t_data, phi1, phi2, phi3):
    """
    Generate tra2.png: time series plot (all phi functions vs time)
    """
    vect = t_data.reshape(-1, 1)  # Make it column vector

    # Create figure and axis with forced square aspect ratio
    fig, ax = plt.subplots(figsize=(10, 10))
    ax.plot(vect, phi1, 'r-', linewidth=2, label=r'$x$')
    ax.plot(vect, phi2, 'g-', linewidth=2, label=r'$y$')
    ax.plot(vect, phi3, 'b-', linewidth=2, label=r'$\theta$')
    ax.set_xlabel(r'$t$', fontsize=12)
    ax.set_ylabel(r'$(x, y, \theta)$', fontsize=12)

    # Set axis tick intervals
    t_max = np.max(t_data)
    ax.set_xticks(np.arange(0, t_max + 1, 2))

    # Set y-axis ticks based on data range
    y_min = min(np.min(phi1), np.min(phi2), np.min(phi3))
    y_max = max(np.max(phi1), np.max(phi2), np.max(phi3))
    ax.set_yticks(np.arange(np.floor(y_min), np.ceil(y_max) + 1, 1))

    # Set equal total length for x and y axes
    x_range = ax.get_xlim()[1] - ax.get_xlim()[0]  # Total x-axis length
    y_range = ax.get_ylim()[1] - ax.get_ylim()[0]  # Total y-axis length

    # Calculate aspect ratio to make total lengths equal
    aspect_ratio = y_range / x_range
    ax.set_aspect(aspect_ratio)
    ax.legend()
    ax.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig('tra2.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("Generated tra2.png")

def plot_tra3_png(phi1, phi2):
    """
    Generate tra3.png: trajectory plot with different style
    """
    # Create figure and axis with forced square aspect ratio
    fig, ax = plt.subplots(figsize=(8, 8))
    ax.plot(phi1, phi2, 'b-', linewidth=2, label=r'$\phi_{NN}$')
    ax.set_xlabel(r'$x$', fontsize=12)
    ax.set_ylabel(r'$y$', fontsize=12)

    # Mark start and end points
    ax.plot(phi1[0], phi2[0], 'go', markersize=8, label='Start')
    ax.plot(phi1[-1], phi2[-1], 'ro', markersize=8, label='End')

    # Set equal total length for x and y axes
    x_range = ax.get_xlim()[1] - ax.get_xlim()[0]  # Total x-axis length
    y_range = ax.get_ylim()[1] - ax.get_ylim()[0]  # Total y-axis length

    # Calculate aspect ratio to make total lengths equal
    aspect_ratio = y_range / x_range
    ax.set_aspect(aspect_ratio)
    ax.legend()
    ax.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig('tra3.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("Generated tra3.png")

def plot_tra4_png(t_data, phi1):
    """
    Generate tra4.png: x vs time plot
    """
    # Create figure and axis with forced square aspect ratio
    fig, ax = plt.subplots(figsize=(8, 8))
    ax.plot(t_data, phi1, 'r-', linewidth=2, label=r'$x(t)$')
    ax.set_xlabel(r'$t$', fontsize=12)
    ax.set_ylabel(r'$x$', fontsize=12)

    # Set equal total length for x and y axes
    x_range = ax.get_xlim()[1] - ax.get_xlim()[0]  # Total x-axis length
    y_range = ax.get_ylim()[1] - ax.get_ylim()[0]  # Total y-axis length

    # Calculate aspect ratio to make total lengths equal
    aspect_ratio = y_range / x_range
    ax.set_aspect(aspect_ratio)
    ax.legend()
    ax.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig('tra4.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("Generated tra4.png")

def plot_tra5_png(t_data, phi3):
    """
    Generate tra5.png: theta vs time plot
    """
    # Create figure and axis with forced square aspect ratio
    fig, ax = plt.subplots(figsize=(8, 8))
    ax.plot(t_data, phi3, 'b-', linewidth=2, label=r'$\theta(t)$')
    ax.set_xlabel(r'$t$', fontsize=12)
    ax.set_ylabel(r'$\theta$', fontsize=12)

    # Set equal total length for x and y axes
    x_range = ax.get_xlim()[1] - ax.get_xlim()[0]  # Total x-axis length
    y_range = ax.get_ylim()[1] - ax.get_ylim()[0]  # Total y-axis length

    # Calculate aspect ratio to make total lengths equal
    aspect_ratio = y_range / x_range
    ax.set_aspect(aspect_ratio)
    ax.legend()
    ax.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig('tra5.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("Generated tra5.png")

def main():
    """
    Main function to generate all plots
    """
    print("Trajectory Plot Generator")
    print("=" * 50)
    
    # Read result files
    t_data, phi1, phi2, phi3 = read_result_files()
    
    if t_data is None:
        print("Failed to read result files. Exiting.")
        return
    
    print("\nGenerating plots...")
    print("-" * 30)
    
    # Generate all plots
    plot_tra_png(phi1, phi2)
    plot_tra2_png(t_data, phi1, phi2, phi3)
    plot_tra3_png(phi1, phi2)
    plot_tra4_png(t_data, phi1)
    plot_tra5_png(t_data, phi3)
    
    print("\nAll plots generated successfully!")
    print("Generated files:")
    print("  - tra.png: Trajectory plot (y vs x)")
    print("  - tra2.png: Time series plot (x, y, theta vs time)")
    print("  - tra3.png: Trajectory plot with start/end markers")
    print("  - tra4.png: x vs time plot")
    print("  - tra5.png: theta vs time plot")

if __name__ == "__main__":
    main()
